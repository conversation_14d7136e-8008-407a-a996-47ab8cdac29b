"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tools/text/page",{

/***/ "(app-pages-browser)/./src/app/tools/text/page.tsx":
/*!*************************************!*\
  !*** ./src/app/tools/text/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextGenerationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst textTypes = [\n    {\n        id: 'blog',\n        name: 'Blog Post',\n        description: 'Create engaging blog content'\n    },\n    {\n        id: 'email',\n        name: 'Email',\n        description: 'Professional email writing'\n    },\n    {\n        id: 'social',\n        name: 'Social Media',\n        description: 'Social media posts and captions'\n    },\n    {\n        id: 'creative',\n        name: 'Creative Writing',\n        description: 'Stories, poems, and creative content'\n    },\n    {\n        id: 'business',\n        name: 'Business Copy',\n        description: 'Marketing and business content'\n    },\n    {\n        id: 'technical',\n        name: 'Technical Writing',\n        description: 'Documentation and technical content'\n    }\n];\nfunction TextGenerationPage() {\n    _s();\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog');\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [generatedText, setGeneratedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGenerate = async ()=>{\n        if (!prompt.trim()) return;\n        setIsGenerating(true);\n        // Simulate AI text generation (replace with actual AI API call)\n        setTimeout(()=>{\n            const sampleTexts = {\n                blog: \"# \".concat(prompt, \"\\n\\nIn today's digital landscape, understanding the importance of \").concat(prompt.toLowerCase(), \" has become crucial for businesses and individuals alike. This comprehensive guide will explore the key aspects and provide actionable insights.\\n\\n## Key Benefits\\n\\n1. **Enhanced Productivity**: By implementing these strategies, you can significantly improve your workflow efficiency.\\n2. **Better Results**: Focus on quality over quantity to achieve meaningful outcomes.\\n3. **Long-term Success**: Building sustainable practices ensures continued growth.\\n\\n## Getting Started\\n\\nTo begin your journey with \").concat(prompt.toLowerCase(), \", consider these essential steps:\\n\\n- Research current best practices\\n- Set clear, measurable goals\\n- Create a structured plan\\n- Monitor progress regularly\\n\\n## Conclusion\\n\\nBy following these guidelines, you'll be well-equipped to succeed in your \").concat(prompt.toLowerCase(), \" endeavors. Remember, consistency and patience are key to achieving lasting results.\"),\n                email: \"Subject: \".concat(prompt, \"\\n\\nDear [Recipient],\\n\\nI hope this email finds you well. I'm writing to discuss \").concat(prompt.toLowerCase(), \" and how it might benefit our collaboration.\\n\\nAfter careful consideration, I believe this presents an excellent opportunity for us to:\\n\\n• Enhance our current processes\\n• Achieve better outcomes\\n• Strengthen our partnership\\n\\nI would appreciate the opportunity to discuss this further at your convenience. Please let me know when you might be available for a brief call or meeting.\\n\\nThank you for your time and consideration.\\n\\nBest regards,\\n[Your Name]\"),\n                social: \"\\uD83C\\uDF1F Excited to share insights about \".concat(prompt, \"! \\n\\n✨ Here's what I've learned:\\n\\n\\uD83D\\uDD39 Quality matters more than quantity\\n\\uD83D\\uDD39 Consistency drives results\\n\\uD83D\\uDD39 Community engagement is key\\n\\n\\uD83D\\uDCA1 What's your experience with \").concat(prompt.toLowerCase(), \"? Share your thoughts below! \\uD83D\\uDC47\\n\\n#Innovation #Growth #Success #\").concat(prompt.replace(/\\s+/g, '')),\n                creative: \"The \".concat(prompt, \"\\n\\nOnce upon a time, in a world not so different from ours, there existed a remarkable phenomenon known as \").concat(prompt.toLowerCase(), '. It was said that those who truly understood its power could achieve extraordinary things.\\n\\nSarah had always been skeptical of such tales, until the day she encountered it herself. The morning sun cast long shadows across her desk as she contemplated the possibilities that lay before her.\\n\\n\"Perhaps,\" she thought, \"there\\'s more to this than meets the eye.\"\\n\\nAs she delved deeper into the mystery, she discovered that ').concat(prompt.toLowerCase(), \" was not just a concept, but a gateway to transformation. Each step forward revealed new layers of understanding, each more fascinating than the last.\\n\\nThe journey had only just begun...\"),\n                business: \"\".concat(prompt, \": Driving Business Excellence\\n\\nExecutive Summary\\n\\nIn today's competitive marketplace, \").concat(prompt.toLowerCase(), \" represents a critical success factor for organizations seeking sustainable growth and market leadership.\\n\\nKey Value Propositions:\\n\\n• Increased operational efficiency by up to 40%\\n• Enhanced customer satisfaction and retention\\n• Improved ROI through strategic implementation\\n• Competitive advantage in the marketplace\\n\\nImplementation Strategy:\\n\\n1. Assessment Phase: Evaluate current capabilities and identify opportunities\\n2. Planning Phase: Develop comprehensive roadmap with clear milestones\\n3. Execution Phase: Deploy solutions with proper change management\\n4. Optimization Phase: Continuous improvement and performance monitoring\\n\\nNext Steps:\\n\\nWe recommend scheduling a strategic planning session to discuss how \").concat(prompt.toLowerCase(), \" can be integrated into your organization's growth strategy.\\n\\nContact us today to learn more about our proven methodology and success stories.\"),\n                technical: \"# \".concat(prompt, \" - Technical Documentation\\n\\n## Overview\\n\\n\").concat(prompt, \" is a comprehensive solution designed to address complex technical challenges in modern software development environments.\\n\\n## System Requirements\\n\\n- Operating System: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)\\n- Memory: Minimum 8GB RAM (16GB recommended)\\n- Storage: 10GB available disk space\\n- Network: Stable internet connection\\n\\n## Installation\\n\\n```bash\\n# Clone the repository\\ngit clone https://github.com/example/\").concat(prompt.toLowerCase().replace(/\\s+/g, '-'), \".git\\n\\n# Navigate to the project directory\\ncd \").concat(prompt.toLowerCase().replace(/\\s+/g, '-'), \"\\n\\n# Install dependencies\\nnpm install\\n\\n# Start the application\\nnpm start\\n```\\n\\n## Configuration\\n\\nCreate a `.env` file in the root directory:\\n\\n```\\nAPI_KEY=your_api_key_here\\nDATABASE_URL=your_database_url\\nPORT=3000\\n```\\n\\n## Usage\\n\\nRefer to the API documentation for detailed usage instructions and examples.\\n\\n## Troubleshooting\\n\\nFor common issues and solutions, please check our FAQ section or contact technical support.\")\n            };\n            setGeneratedText(sampleTexts[selectedType] || 'Generated text would appear here...');\n            setIsGenerating(false);\n        }, 2000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-12 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"AI Text Generation\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create compelling content with AI assistance. Choose your content type and provide a prompt to generate high-quality text.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"Content Configuration\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                            children: \"Content Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: textTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedType(type.id),\n                                                    className: \"p-3 text-left rounded-lg border transition-colors \".concat(selectedType === type.id ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: type.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: type.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, type.id, true, {\n                                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"prompt\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Content Prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"prompt\",\n                                            rows: 4,\n                                            value: prompt,\n                                            onChange: (e)=>setPrompt(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-black placeholder-gray-500\",\n                                            placeholder: \"Describe what you want to write about...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGenerate,\n                                    disabled: !prompt.trim() || isGenerating,\n                                    className: \"w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Generating...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Generate Content\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"Generated Content\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                generatedText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"whitespace-pre-wrap text-sm text-gray-700 font-mono\",\n                                                children: generatedText\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigator.clipboard.writeText(generatedText),\n                                            className: \"w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                            children: \"Copy to Clipboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-64 text-gray-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Generated content will appear here\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\text\\\\page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(TextGenerationPage, \"SOaZ9NQORGG3wCkMvSIIdkpU+zo=\");\n_c = TextGenerationPage;\nvar _c;\n$RefreshReg$(_c, \"TextGenerationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tools/text/page.tsx\n"));

/***/ })

});