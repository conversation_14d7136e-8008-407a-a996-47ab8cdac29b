'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, SparklesIcon, ClockIcon } from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  url: string;
  type: 'web' | 'academic' | 'news' | 'image';
  relevance: number;
  date?: string;
}

export default function SmartSearchPage() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchType, setSearchType] = useState<'all' | 'web' | 'academic' | 'news' | 'images'>('all');

  const searchTypes = [
    { id: 'all', name: 'All Results', icon: '🔍' },
    { id: 'web', name: 'Web', icon: '🌐' },
    { id: 'academic', name: 'Academic', icon: '📚' },
    { id: 'news', name: 'News', icon: '📰' },
    { id: 'images', name: 'Images', icon: '🖼️' },
  ];

  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setIsSearching(true);
    
    // Simulate AI-powered search (replace with actual search API)
    setTimeout(() => {
      const sampleResults: SearchResult[] = [
        {
          id: '1',
          title: `Understanding ${query}: A Comprehensive Guide`,
          description: `Learn everything you need to know about ${query.toLowerCase()} with this detailed guide covering key concepts, best practices, and real-world applications.`,
          url: 'https://example.com/guide',
          type: 'web',
          relevance: 0.95,
          date: '2024-01-15'
        },
        {
          id: '2',
          title: `Latest Research on ${query}`,
          description: `Recent academic research exploring the implications and future directions of ${query.toLowerCase()} in modern applications.`,
          url: 'https://academic.example.com/research',
          type: 'academic',
          relevance: 0.92,
          date: '2024-01-10'
        },
        {
          id: '3',
          title: `${query} in the News: Recent Developments`,
          description: `Breaking news and recent developments related to ${query.toLowerCase()}, including industry insights and expert opinions.`,
          url: 'https://news.example.com/article',
          type: 'news',
          relevance: 0.88,
          date: '2024-01-20'
        },
        {
          id: '4',
          title: `How to Implement ${query} Successfully`,
          description: `Step-by-step tutorial on implementing ${query.toLowerCase()} with practical examples and troubleshooting tips.`,
          url: 'https://tutorial.example.com/implementation',
          type: 'web',
          relevance: 0.85,
          date: '2024-01-12'
        },
        {
          id: '5',
          title: `${query}: Case Studies and Examples`,
          description: `Real-world case studies showcasing successful applications of ${query.toLowerCase()} across different industries.`,
          url: 'https://casestudies.example.com/examples',
          type: 'web',
          relevance: 0.82,
          date: '2024-01-08'
        }
      ];

      const filteredResults = searchType === 'all' 
        ? sampleResults 
        : sampleResults.filter(result => result.type === searchType);

      setResults(filteredResults);
      setIsSearching(false);
    }, 1500);
  };

  const handleQueryChange = (value: string) => {
    setQuery(value);
    
    // Generate AI-powered suggestions
    if (value.length > 2) {
      const sampleSuggestions = [
        `${value} best practices`,
        `${value} tutorial`,
        `${value} examples`,
        `${value} vs alternatives`,
        `${value} implementation guide`
      ];
      setSuggestions(sampleSuggestions);
    } else {
      setSuggestions([]);
    }
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      web: '🌐',
      academic: '📚',
      news: '📰',
      image: '🖼️'
    };
    return icons[type as keyof typeof icons] || '📄';
  };

  const getRelevanceColor = (relevance: number) => {
    if (relevance >= 0.9) return 'text-green-600';
    if (relevance >= 0.8) return 'text-yellow-600';
    return 'text-gray-600';
  };

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <MagnifyingGlassIcon className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">AI Smart Search</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Enhanced search with AI-powered suggestions, semantic understanding, and intelligent result ranking.
          </p>
        </div>

        {/* Search Interface */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="space-y-4">
            {/* Search Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={query}
                onChange={(e) => handleQueryChange(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-lg text-black placeholder-gray-500"
                placeholder="Enter your search query..."
              />
            </div>

            {/* Search Type Filters */}
            <div className="flex flex-wrap gap-2">
              {searchTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSearchType(type.id as any)}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                    searchType === type.id
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type.icon} {type.name}
                </button>
              ))}
            </div>

            {/* Search Button */}
            <button
              onClick={handleSearch}
              disabled={!query.trim() || isSearching}
              className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isSearching ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  Smart Search
                </>
              )}
            </button>

            {/* Suggestions */}
            {suggestions.length > 0 && (
              <div className="border-t pt-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Suggested searches:</p>
                <div className="flex flex-wrap gap-2">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setQuery(suggestion);
                        setSuggestions([]);
                      }}
                      className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm hover:bg-blue-100 transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Search Results */}
        {results.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Search Results ({results.length})
              </h2>
              <span className="text-sm text-gray-500">
                Sorted by relevance
              </span>
            </div>

            {results.map((result) => (
              <div
                key={result.id}
                className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getTypeIcon(result.type)}</span>
                    <span className="text-xs uppercase font-medium text-gray-500">
                      {result.type}
                    </span>
                    <span className={`text-xs font-medium ${getRelevanceColor(result.relevance)}`}>
                      {Math.round(result.relevance * 100)}% match
                    </span>
                  </div>
                  {result.date && (
                    <div className="flex items-center text-xs text-gray-500">
                      <ClockIcon className="w-3 h-3 mr-1" />
                      {result.date}
                    </div>
                  )}
                </div>

                <h3 className="text-lg font-semibold text-blue-600 hover:text-blue-800 mb-2">
                  <a href={result.url} target="_blank" rel="noopener noreferrer">
                    {result.title}
                  </a>
                </h3>

                <p className="text-gray-600 text-sm mb-3">
                  {result.description}
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 truncate">
                    {result.url}
                  </span>
                  <a
                    href={result.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Visit →
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No Results */}
        {!isSearching && query && results.length === 0 && (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No results found. Try a different search query.</p>
          </div>
        )}
      </div>
    </div>
  );
}
