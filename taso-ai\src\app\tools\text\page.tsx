'use client';

import { useState } from 'react';
import { PencilSquareIcon, SparklesIcon } from '@heroicons/react/24/outline';

const textTypes = [
  { id: 'blog', name: 'Blog Post', description: 'Create engaging blog content' },
  { id: 'email', name: 'Email', description: 'Professional email writing' },
  { id: 'social', name: 'Social Media', description: 'Social media posts and captions' },
  { id: 'creative', name: 'Creative Writing', description: 'Stories, poems, and creative content' },
  { id: 'business', name: 'Business Copy', description: 'Marketing and business content' },
  { id: 'technical', name: 'Technical Writing', description: 'Documentation and technical content' },
];

export default function TextGenerationPage() {
  const [selectedType, setSelectedType] = useState('blog');
  const [prompt, setPrompt] = useState('');
  const [generatedText, setGeneratedText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    // Simulate AI text generation (replace with actual AI API call)
    setTimeout(() => {
      const sampleTexts = {
        blog: `# ${prompt}\n\nIn today's digital landscape, understanding the importance of ${prompt.toLowerCase()} has become crucial for businesses and individuals alike. This comprehensive guide will explore the key aspects and provide actionable insights.\n\n## Key Benefits\n\n1. **Enhanced Productivity**: By implementing these strategies, you can significantly improve your workflow efficiency.\n2. **Better Results**: Focus on quality over quantity to achieve meaningful outcomes.\n3. **Long-term Success**: Building sustainable practices ensures continued growth.\n\n## Getting Started\n\nTo begin your journey with ${prompt.toLowerCase()}, consider these essential steps:\n\n- Research current best practices\n- Set clear, measurable goals\n- Create a structured plan\n- Monitor progress regularly\n\n## Conclusion\n\nBy following these guidelines, you'll be well-equipped to succeed in your ${prompt.toLowerCase()} endeavors. Remember, consistency and patience are key to achieving lasting results.`,
        email: `Subject: ${prompt}\n\nDear [Recipient],\n\nI hope this email finds you well. I'm writing to discuss ${prompt.toLowerCase()} and how it might benefit our collaboration.\n\nAfter careful consideration, I believe this presents an excellent opportunity for us to:\n\n• Enhance our current processes\n• Achieve better outcomes\n• Strengthen our partnership\n\nI would appreciate the opportunity to discuss this further at your convenience. Please let me know when you might be available for a brief call or meeting.\n\nThank you for your time and consideration.\n\nBest regards,\n[Your Name]`,
        social: `🌟 Excited to share insights about ${prompt}! \n\n✨ Here's what I've learned:\n\n🔹 Quality matters more than quantity\n🔹 Consistency drives results\n🔹 Community engagement is key\n\n💡 What's your experience with ${prompt.toLowerCase()}? Share your thoughts below! 👇\n\n#Innovation #Growth #Success #${prompt.replace(/\s+/g, '')}`,
        creative: `The ${prompt}\n\nOnce upon a time, in a world not so different from ours, there existed a remarkable phenomenon known as ${prompt.toLowerCase()}. It was said that those who truly understood its power could achieve extraordinary things.\n\nSarah had always been skeptical of such tales, until the day she encountered it herself. The morning sun cast long shadows across her desk as she contemplated the possibilities that lay before her.\n\n"Perhaps," she thought, "there's more to this than meets the eye."\n\nAs she delved deeper into the mystery, she discovered that ${prompt.toLowerCase()} was not just a concept, but a gateway to transformation. Each step forward revealed new layers of understanding, each more fascinating than the last.\n\nThe journey had only just begun...`,
        business: `${prompt}: Driving Business Excellence\n\nExecutive Summary\n\nIn today's competitive marketplace, ${prompt.toLowerCase()} represents a critical success factor for organizations seeking sustainable growth and market leadership.\n\nKey Value Propositions:\n\n• Increased operational efficiency by up to 40%\n• Enhanced customer satisfaction and retention\n• Improved ROI through strategic implementation\n• Competitive advantage in the marketplace\n\nImplementation Strategy:\n\n1. Assessment Phase: Evaluate current capabilities and identify opportunities\n2. Planning Phase: Develop comprehensive roadmap with clear milestones\n3. Execution Phase: Deploy solutions with proper change management\n4. Optimization Phase: Continuous improvement and performance monitoring\n\nNext Steps:\n\nWe recommend scheduling a strategic planning session to discuss how ${prompt.toLowerCase()} can be integrated into your organization's growth strategy.\n\nContact us today to learn more about our proven methodology and success stories.`,
        technical: `# ${prompt} - Technical Documentation\n\n## Overview\n\n${prompt} is a comprehensive solution designed to address complex technical challenges in modern software development environments.\n\n## System Requirements\n\n- Operating System: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)\n- Memory: Minimum 8GB RAM (16GB recommended)\n- Storage: 10GB available disk space\n- Network: Stable internet connection\n\n## Installation\n\n\`\`\`bash\n# Clone the repository\ngit clone https://github.com/example/${prompt.toLowerCase().replace(/\s+/g, '-')}.git\n\n# Navigate to the project directory\ncd ${prompt.toLowerCase().replace(/\s+/g, '-')}\n\n# Install dependencies\nnpm install\n\n# Start the application\nnpm start\n\`\`\`\n\n## Configuration\n\nCreate a \`.env\` file in the root directory:\n\n\`\`\`\nAPI_KEY=your_api_key_here\nDATABASE_URL=your_database_url\nPORT=3000\n\`\`\`\n\n## Usage\n\nRefer to the API documentation for detailed usage instructions and examples.\n\n## Troubleshooting\n\nFor common issues and solutions, please check our FAQ section or contact technical support.`
      };
      
      setGeneratedText(sampleTexts[selectedType as keyof typeof sampleTexts] || 'Generated text would appear here...');
      setIsGenerating(false);
    }, 2000);
  };

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <PencilSquareIcon className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">AI Text Generation</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create compelling content with AI assistance. Choose your content type and provide a prompt to generate high-quality text.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Content Configuration</h2>
            
            {/* Content Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Content Type
              </label>
              <div className="grid grid-cols-2 gap-3">
                {textTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setSelectedType(type.id)}
                    className={`p-3 text-left rounded-lg border transition-colors ${
                      selectedType === type.id
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-sm">{type.name}</div>
                    <div className="text-xs text-gray-500 mt-1">{type.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Prompt Input */}
            <div className="mb-6">
              <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                Content Prompt
              </label>
              <textarea
                id="prompt"
                rows={4}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-black placeholder-gray-500"
                placeholder="Describe what you want to write about..."
              />
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  Generate Content
                </>
              )}
            </button>
          </div>

          {/* Output Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Generated Content</h2>
            
            {generatedText ? (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono">
                    {generatedText}
                  </pre>
                </div>
                <button
                  onClick={() => navigator.clipboard.writeText(generatedText)}
                  className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Copy to Clipboard
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <PencilSquareIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Generated content will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
