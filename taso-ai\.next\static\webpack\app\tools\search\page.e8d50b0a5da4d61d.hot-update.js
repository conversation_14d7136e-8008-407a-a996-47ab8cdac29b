"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tools/search/page",{

/***/ "(app-pages-browser)/./src/app/tools/search/page.tsx":
/*!***************************************!*\
  !*** ./src/app/tools/search/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartSearchPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SmartSearchPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchType, setSearchType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const searchTypes = [\n        {\n            id: 'all',\n            name: 'All Results',\n            icon: '🔍'\n        },\n        {\n            id: 'web',\n            name: 'Web',\n            icon: '🌐'\n        },\n        {\n            id: 'academic',\n            name: 'Academic',\n            icon: '📚'\n        },\n        {\n            id: 'news',\n            name: 'News',\n            icon: '📰'\n        },\n        {\n            id: 'images',\n            name: 'Images',\n            icon: '🖼️'\n        }\n    ];\n    const handleSearch = async ()=>{\n        if (!query.trim()) return;\n        setIsSearching(true);\n        // Simulate AI-powered search (replace with actual search API)\n        setTimeout(()=>{\n            const sampleResults = [\n                {\n                    id: '1',\n                    title: \"Understanding \".concat(query, \": A Comprehensive Guide\"),\n                    description: \"Learn everything you need to know about \".concat(query.toLowerCase(), \" with this detailed guide covering key concepts, best practices, and real-world applications.\"),\n                    url: 'https://example.com/guide',\n                    type: 'web',\n                    relevance: 0.95,\n                    date: '2024-01-15'\n                },\n                {\n                    id: '2',\n                    title: \"Latest Research on \".concat(query),\n                    description: \"Recent academic research exploring the implications and future directions of \".concat(query.toLowerCase(), \" in modern applications.\"),\n                    url: 'https://academic.example.com/research',\n                    type: 'academic',\n                    relevance: 0.92,\n                    date: '2024-01-10'\n                },\n                {\n                    id: '3',\n                    title: \"\".concat(query, \" in the News: Recent Developments\"),\n                    description: \"Breaking news and recent developments related to \".concat(query.toLowerCase(), \", including industry insights and expert opinions.\"),\n                    url: 'https://news.example.com/article',\n                    type: 'news',\n                    relevance: 0.88,\n                    date: '2024-01-20'\n                },\n                {\n                    id: '4',\n                    title: \"How to Implement \".concat(query, \" Successfully\"),\n                    description: \"Step-by-step tutorial on implementing \".concat(query.toLowerCase(), \" with practical examples and troubleshooting tips.\"),\n                    url: 'https://tutorial.example.com/implementation',\n                    type: 'web',\n                    relevance: 0.85,\n                    date: '2024-01-12'\n                },\n                {\n                    id: '5',\n                    title: \"\".concat(query, \": Case Studies and Examples\"),\n                    description: \"Real-world case studies showcasing successful applications of \".concat(query.toLowerCase(), \" across different industries.\"),\n                    url: 'https://casestudies.example.com/examples',\n                    type: 'web',\n                    relevance: 0.82,\n                    date: '2024-01-08'\n                }\n            ];\n            const filteredResults = searchType === 'all' ? sampleResults : sampleResults.filter((result)=>result.type === searchType);\n            setResults(filteredResults);\n            setIsSearching(false);\n        }, 1500);\n    };\n    const handleQueryChange = (value)=>{\n        setQuery(value);\n        // Generate AI-powered suggestions\n        if (value.length > 2) {\n            const sampleSuggestions = [\n                \"\".concat(value, \" best practices\"),\n                \"\".concat(value, \" tutorial\"),\n                \"\".concat(value, \" examples\"),\n                \"\".concat(value, \" vs alternatives\"),\n                \"\".concat(value, \" implementation guide\")\n            ];\n            setSuggestions(sampleSuggestions);\n        } else {\n            setSuggestions([]);\n        }\n    };\n    const getTypeIcon = (type)=>{\n        const icons = {\n            web: '🌐',\n            academic: '📚',\n            news: '📰',\n            image: '🖼️'\n        };\n        return icons[type] || '📄';\n    };\n    const getRelevanceColor = (relevance)=>{\n        if (relevance >= 0.9) return 'text-green-600';\n        if (relevance >= 0.8) return 'text-yellow-600';\n        return 'text-gray-600';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-12 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"AI Smart Search\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Enhanced search with AI-powered suggestions, semantic understanding, and intelligent result ranking.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: query,\n                                        onChange: (e)=>handleQueryChange(e.target.value),\n                                        onKeyPress: (e)=>e.key === 'Enter' && handleSearch(),\n                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-lg text-black placeholder-gray-500\",\n                                        placeholder: \"Enter your search query...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: searchTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSearchType(type.id),\n                                        className: \"px-3 py-1.5 rounded-full text-sm font-medium transition-colors \".concat(searchType === type.id ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: [\n                                            type.icon,\n                                            \" \",\n                                            type.name\n                                        ]\n                                    }, type.id, true, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSearch,\n                                disabled: !query.trim() || isSearching,\n                                className: \"w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                children: isSearching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Searching...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Smart Search\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Suggested searches:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setQuery(suggestion);\n                                                    setSuggestions([]);\n                                                },\n                                                className: \"px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm hover:bg-blue-100 transition-colors\",\n                                                children: suggestion\n                                            }, index, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: [\n                                        \"Search Results (\",\n                                        results.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Sorted by relevance\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: getTypeIcon(result.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs uppercase font-medium text-gray-500\",\n                                                        children: result.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(getRelevanceColor(result.relevance)),\n                                                        children: [\n                                                            Math.round(result.relevance * 100),\n                                                            \"% match\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            result.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-3 h-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    result.date\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-600 hover:text-blue-800 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: result.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: result.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-3\",\n                                        children: result.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 truncate\",\n                                                children: result.url\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: result.url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                                children: \"Visit →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, result.id, true, {\n                                fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this),\n                !isSearching && query && results.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No results found. Try a different search query.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TasoAI\\\\taso-ai\\\\src\\\\app\\\\tools\\\\search\\\\page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartSearchPage, \"lW3zm51SQqAeIUUbuAl22gVop80=\");\n_c = SmartSearchPage;\nvar _c;\n$RefreshReg$(_c, \"SmartSearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tools/search/page.tsx\n"));

/***/ })

});