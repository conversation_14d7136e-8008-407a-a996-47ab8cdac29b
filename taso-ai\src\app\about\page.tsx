import { 
  SparklesIcon, 
  BoltIcon, 
  CpuChipIcon,
  UserGroupIcon,
  LightBulbIcon,
  ShieldCheckIcon 
} from '@heroicons/react/24/outline';

export default function AboutPage() {
  const features = [
    {
      name: 'Cutting-Edge AI',
      description: 'Powered by the latest artificial intelligence models and machine learning technologies.',
      icon: CpuChipIcon,
    },
    {
      name: 'User-Friendly Design',
      description: 'Intuitive interfaces that make powerful AI tools accessible to everyone.',
      icon: SparklesIcon,
    },
    {
      name: 'Lightning Fast',
      description: 'Optimized performance ensures you get results quickly and efficiently.',
      icon: BoltIcon,
    },
    {
      name: 'Privacy First',
      description: 'Your data security and privacy are our top priorities in everything we build.',
      icon: ShieldCheckIcon,
    },
    {
      name: 'Continuous Innovation',
      description: 'Regular updates and new features to keep you at the forefront of AI technology.',
      icon: LightBulbIcon,
    },
    {
      name: 'Community Driven',
      description: 'Built with feedback from our users to solve real-world problems.',
      icon: UserGroupIcon,
    },
  ];

  const team = [
    {
      name: 'AI Research Team',
      role: 'Machine Learning Engineers',
      description: 'Developing and optimizing AI models for practical applications.',
    },
    {
      name: 'Product Team',
      role: 'UX/UI Designers',
      description: 'Creating intuitive and accessible user experiences.',
    },
    {
      name: 'Engineering Team',
      role: 'Full-Stack Developers',
      description: 'Building robust and scalable AI-powered applications.',
    },
  ];

  return (
    <div className="py-24 sm:py-32 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
            About Taso
          </h1>
          <p className="text-xl leading-8 text-gray-600 max-w-3xl mx-auto">
            We're on a mission to make artificial intelligence accessible and practical for everyone. 
            Taso brings together cutting-edge AI technology with intuitive design to create tools 
            that enhance your daily productivity and creativity.
          </p>
        </div>

        {/* Mission Statement */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-20">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-lg text-gray-700 max-w-4xl mx-auto">
              To democratize artificial intelligence by creating powerful, user-friendly tools that 
              help people accomplish more in their personal and professional lives. We believe AI 
              should be a force for empowerment, not intimidation.
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-4">
              What Makes Taso Special
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our commitment to excellence drives everything we do, from the AI models we choose 
              to the interfaces we design.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature) => (
              <div key={feature.name} className="relative">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mb-4">
                  <feature.icon className="h-6 w-6" aria-hidden="true" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{feature.name}</h3>
                <p className="text-base text-gray-500">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-4">
              Our Values
            </h2>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <SparklesIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Innovation</h3>
              <p className="text-gray-600">
                We constantly push the boundaries of what's possible with AI technology, 
                always looking for new ways to solve problems and create value.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <UserGroupIcon className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Accessibility</h3>
              <p className="text-gray-600">
                AI should be for everyone. We design our tools to be intuitive and accessible, 
                regardless of technical background or experience level.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <ShieldCheckIcon className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Trust</h3>
              <p className="text-gray-600">
                We build trust through transparency, security, and reliability. Your data 
                and privacy are always protected with the highest standards.
              </p>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-4">
              Our Team
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Taso is built by a diverse team of AI researchers, engineers, and designers 
              who are passionate about making technology work better for people.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {team.map((member, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{member.name}</h3>
                <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gray-900 rounded-2xl p-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Experience AI?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of users who are already using Taso to enhance their productivity 
            and unlock new possibilities with AI.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/tools"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-white hover:bg-gray-50 transition-colors"
            >
              Explore AI Tools
            </a>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors"
            >
              Get in Touch
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
