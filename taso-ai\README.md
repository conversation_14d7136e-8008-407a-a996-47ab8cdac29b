# Taso - Everyday AI Tools

A modern web application that showcases practical artificial intelligence tools for everyday use. Built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

### AI-Powered Tools
- **Text Generation** - Create compelling content with AI assistance
- **Image Analysis** - Upload and analyze images with detailed AI insights
- **Smart Search** - Enhanced search with AI-powered suggestions
- **AI Assistant** - Interactive chatbot for questions and problem-solving

### Modern Web Experience
- **Responsive Design** - Works seamlessly on desktop and mobile
- **Fast Performance** - Optimized with Next.js and modern web technologies
- **Intuitive UI** - Clean, professional design with smooth animations
- **Accessibility** - Built with accessibility best practices

## 🛠️ Technology Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Icons**: Heroicons
- **Animations**: Framer Motion
- **Fonts**: Geist Sans & Geist Mono

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd taso-ai
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
taso-ai/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── about/          # About page
│   │   ├── contact/        # Contact page
│   │   ├── tools/          # AI tools pages
│   │   │   ├── text/       # Text generation tool
│   │   │   ├── image/      # Image analysis tool
│   │   │   ├── search/     # Smart search tool
│   │   │   └── chat/       # AI assistant chat
│   │   ├── layout.tsx      # Root layout
│   │   ├── page.tsx        # Home page
│   │   └── globals.css     # Global styles
│   └── components/         # Reusable React components
│       ├── Header.tsx      # Navigation header
│       ├── Footer.tsx      # Site footer
│       ├── Hero.tsx        # Landing page hero
│       └── Features.tsx    # Features showcase
├── public/                 # Static assets
├── package.json           # Dependencies and scripts
└── README.md             # Project documentation
```

## 🎯 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🔧 Configuration

The project uses modern Next.js configuration with:
- TypeScript for type safety
- Tailwind CSS for styling
- ESLint for code quality
- Automatic font optimization

## 🚀 Deployment

This project can be deployed on various platforms:

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🔮 Future Enhancements

- User authentication and profiles
- API integrations with AI services (OpenAI, Anthropic)
- Database integration for user data
- Advanced AI features and tools
- Mobile app development
- Enterprise features

## 📞 Support

For questions or support, please visit our [Contact Page](http://localhost:3000/contact) or reach out to <NAME_EMAIL>.

---

Built with ❤️ using Next.js and modern web technologies.
